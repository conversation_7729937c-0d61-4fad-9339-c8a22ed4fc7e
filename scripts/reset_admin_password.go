package main

import (
	"fmt"
	"log"
	"os"

	"golang.org/x/crypto/bcrypt"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// User 用户模型
type User struct {
	ID       uint   `gorm:"primaryKey;autoIncrement"`
	Phone    string `gorm:"type:varchar(20);uniqueIndex;not null"`
	Password string `gorm:"type:varchar(255);not null"`
	Role     string `gorm:"type:enum('admin','manager','user');default:'user'"`
	Nickname string `gorm:"type:varchar(100)"`
	Balance  int64  `gorm:"default:0"`
	IsActive bool   `gorm:"default:true"`
}

// TableName 指定表名
func (User) TableName() string {
	return "hook_user"
}

func main() {
	// 数据库连接配置
	dbHost := getEnv("DB_HOST", "localhost")
	dbPort := getEnv("DB_PORT", "3306")
	dbUser := getEnv("DB_USER", "root")
	dbPassword := getEnv("DB_PASSWORD", "root123456")
	dbName := getEnv("DB_NAME", "solve_web")

	// 构建DSN
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		dbUser, dbPassword, dbHost, dbPort, dbName)

	// 连接数据库
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		log.Fatalf("连接数据库失败: %v", err)
	}

	// 获取底层sql.DB对象
	sqlDB, err := db.DB()
	if err != nil {
		log.Fatalf("获取数据库连接失败: %v", err)
	}
	defer sqlDB.Close()

	// 生成新密码的哈希值
	newPassword := "123456"
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(newPassword), bcrypt.DefaultCost)
	if err != nil {
		log.Fatalf("生成密码哈希失败: %v", err)
	}

	fmt.Printf("新密码哈希值: %s\n", string(hashedPassword))

	// 查找所有管理员和题库管理员
	var users []User
	err = db.Where("role IN ?", []string{"admin", "manager"}).Find(&users).Error
	if err != nil {
		log.Fatalf("查询管理员用户失败: %v", err)
	}

	if len(users) == 0 {
		log.Println("未找到管理员用户，开始创建默认管理员...")
		
		// 创建默认管理员
		defaultUsers := []User{
			{
				Phone:    "13800000001",
				Password: string(hashedPassword),
				Role:     "admin",
				Nickname: "超级管理员",
				Balance:  0,
				IsActive: true,
			},
			{
				Phone:    "13800000002",
				Password: string(hashedPassword),
				Role:     "manager",
				Nickname: "题库管理员1",
				Balance:  0,
				IsActive: true,
			},
			{
				Phone:    "13800000003",
				Password: string(hashedPassword),
				Role:     "manager",
				Nickname: "题库管理员2",
				Balance:  0,
				IsActive: true,
			},
		}

		for _, user := range defaultUsers {
			err = db.Create(&user).Error
			if err != nil {
				log.Printf("创建用户失败 %s: %v", user.Nickname, err)
			} else {
				fmt.Printf("✅ 创建用户成功: %s (手机号: %s, 角色: %s)\n", 
					user.Nickname, user.Phone, user.Role)
			}
		}
	} else {
		// 重置现有管理员密码
		fmt.Printf("找到 %d 个管理员用户，开始重置密码...\n", len(users))
		
		for _, user := range users {
			err = db.Model(&user).Update("password", string(hashedPassword)).Error
			if err != nil {
				log.Printf("重置用户密码失败 %s: %v", user.Nickname, err)
			} else {
				fmt.Printf("✅ 重置密码成功: %s (手机号: %s, 角色: %s)\n", 
					user.Nickname, user.Phone, user.Role)
			}
		}
	}

	fmt.Println("\n🎉 管理员密码重置完成！")
	fmt.Println("新密码: 123456")
	fmt.Println("\n管理员账户信息:")
	fmt.Println("超级管理员 - 手机号: 13800000001, 密码: 123456")
	fmt.Println("题库管理员1 - 手机号: 13800000002, 密码: 123456")
	fmt.Println("题库管理员2 - 手机号: 13800000003, 密码: 123456")
}

// getEnv 获取环境变量，如果不存在则返回默认值
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
