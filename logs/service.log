
2025/06/16 11:58:32 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[29.024ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/16 11:58:32 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.481ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/16 11:58:32 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.680ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.tables WHERE table_schema = 'solve_web' AND table_name = 'hook_user' AND table_type = 'BASE TABLE'

2025/06/16 11:58:32 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[30.396ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/16 11:58:32 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[61.984ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/16 11:58:32 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[57.001ms] [34;1m[rows:-][0m SELECT * FROM `hook_user` LIMIT 1

2025/06/16 11:58:32 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[60.019ms] [34;1m[rows:-][0m SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'solve_web' AND table_name = 'hook_user' ORDER BY ORDINAL_POSITION

2025/06/16 11:58:32 [32m
[0m[33m[29.805ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/16 11:58:32 [32m
[0m[33m[59.402ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/16 11:58:32 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[60.248ms] [34;1m[rows:2][0m 
SELECT
	TABLE_NAME,
	COLUMN_NAME,
	INDEX_NAME,
	NON_UNIQUE 
FROM
	information_schema.STATISTICS 
WHERE
	TABLE_SCHEMA = 'solve_web' 
	AND TABLE_NAME = 'hook_user' 
ORDER BY
	INDEX_NAME,
	SEQ_IN_INDEX

2025/06/16 11:58:32 [32m
[0m[33m[29.296ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/16 11:58:32 [32m
[0m[33m[65.807ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/16 11:58:32 [32m
[0m[33m[63.516ms] [34;1m[rows:-][0m SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'solve_web' AND table_name = 'hook_user' AND constraint_name = 'uni_hook_user_phone'

2025/06/16 11:58:32 [32m
[0m[33m[29.704ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/16 11:58:32 [32m
[0m[33m[59.939ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/16 11:58:32 [32m
[0m[33m[60.486ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'solve_web' AND table_name = 'hook_user' AND index_name = 'idx_hook_user_phone'

2025/06/16 11:58:32 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[29.408ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/16 11:58:33 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[60.999ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/16 11:58:33 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[68.352ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'solve_web' AND table_name = 'hook_user' AND index_name = 'idx_hook_user_phone'

2025/06/16 11:58:33 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[29.944ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/16 11:58:33 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[60.074ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/16 11:58:33 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.284ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.tables WHERE table_schema = 'solve_web' AND table_name = 'hook_apps' AND table_type = 'BASE TABLE'

2025/06/16 11:58:33 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[30.444ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/16 11:58:33 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[58.592ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/16 11:58:33 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[60.129ms] [34;1m[rows:-][0m SELECT * FROM `hook_apps` LIMIT 1

2025/06/16 11:58:33 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.759ms] [34;1m[rows:-][0m SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'solve_web' AND table_name = 'hook_apps' ORDER BY ORDINAL_POSITION

2025/06/16 11:58:33 [32m
[0m[33m[29.824ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/16 11:58:33 [32m
[0m[33m[59.190ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/16 11:58:33 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.924ms] [34;1m[rows:3][0m 
SELECT
	TABLE_NAME,
	COLUMN_NAME,
	INDEX_NAME,
	NON_UNIQUE 
FROM
	information_schema.STATISTICS 
WHERE
	TABLE_SCHEMA = 'solve_web' 
	AND TABLE_NAME = 'hook_apps' 
ORDER BY
	INDEX_NAME,
	SEQ_IN_INDEX

2025/06/16 11:58:33 [32m
[0m[33m[30.571ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/16 11:58:33 [32m
[0m[33m[59.230ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/16 11:58:33 [32m
[0m[33m[59.080ms] [34;1m[rows:-][0m SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'solve_web' AND table_name = 'hook_apps' AND constraint_name = 'uni_hook_apps_app_id'

2025/06/16 11:58:33 [32m
[0m[33m[30.159ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/16 11:58:33 [32m
[0m[33m[59.567ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/16 11:58:33 [32m
[0m[33m[59.662ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'solve_web' AND table_name = 'hook_apps' AND index_name = 'idx_hook_apps_app_id'

2025/06/16 11:58:33 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[29.887ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/16 11:58:34 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.693ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/16 11:58:34 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.631ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'solve_web' AND table_name = 'hook_apps' AND index_name = 'idx_hook_apps_user_id'

2025/06/16 11:58:34 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[29.541ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/16 11:58:34 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[60.057ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/16 11:58:34 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.477ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'solve_web' AND table_name = 'hook_apps' AND index_name = 'idx_hook_apps_app_id'
