package main

import (
	"fmt"
	"log"
	"os"

	"golang.org/x/crypto/bcrypt"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// User 用户模型
type User struct {
	ID       uint   `gorm:"primaryKey;autoIncrement"`
	Phone    string `gorm:"type:varchar(20);uniqueIndex;not null"`
	Password string `gorm:"type:varchar(255);not null"`
	Role     string `gorm:"type:enum('admin','manager','user');default:'user'"`
	Nickname string `gorm:"type:varchar(100)"`
	Balance  int64  `gorm:"default:0"`
	IsActive bool   `gorm:"default:true"`
}

// TableName 指定表名
func (User) TableName() string {
	return "hook_user"
}

func main() {
	// 数据库连接配置
	// 注意：使用远程数据库配置，避免本地配置问题
	dbHost := getEnv("DB_HOST", "***********")          // 远程MySQL服务器
	dbPort := getEnv("DB_PORT", "3380")                 // 远程MySQL端口
	dbUser := getEnv("DB_USERNAME", "gmdns")            // 远程MySQL用户名
	dbPassword := getEnv("DB_PASSWORD", "Suyan15913..") // 远程MySQL密码
	dbName := getEnv("DB_DATABASE", "solve_web")        // 数据库名称

	// 构建DSN
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		dbUser, dbPassword, dbHost, dbPort, dbName)

	fmt.Printf("连接数据库: %s\n", dsn)

	// 连接数据库
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		log.Fatalf("连接数据库失败: %v", err)
	}

	// 获取底层sql.DB对象
	sqlDB, err := db.DB()
	if err != nil {
		log.Fatalf("获取数据库连接失败: %v", err)
	}
	defer sqlDB.Close()

	fmt.Println("数据库连接成功")

	// 生成正确的密码哈希
	newPassword := "123456"
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(newPassword), bcrypt.DefaultCost)
	if err != nil {
		log.Fatalf("生成密码哈希失败: %v", err)
	}

	fmt.Printf("新密码: %s\n", newPassword)
	fmt.Printf("新密码哈希: %s\n", string(hashedPassword))

	// 验证新哈希是否正确
	err = bcrypt.CompareHashAndPassword(hashedPassword, []byte(newPassword))
	if err != nil {
		log.Fatalf("新密码哈希验证失败: %v", err)
	}
	fmt.Println("✅ 新密码哈希验证成功")

	// 更新所有管理员用户的密码
	result := db.Model(&User{}).
		Where("role IN ?", []string{"admin", "manager"}).
		Updates(map[string]interface{}{
			"password":  string(hashedPassword),
			"is_active": true, // 确保账户激活
		})

	if result.Error != nil {
		log.Fatalf("更新密码失败: %v", result.Error)
	}

	fmt.Printf("✅ 成功更新 %d 个管理员账户的密码\n", result.RowsAffected)

	// 验证更新结果
	fmt.Println("\n🔍 验证更新结果:")
	var users []User
	err = db.Where("role IN ?", []string{"admin", "manager"}).Find(&users).Error
	if err != nil {
		log.Fatalf("查询用户失败: %v", err)
	}

	for _, user := range users {
		fmt.Printf("\n用户: %s (%s)\n", user.Nickname, user.Phone)
		fmt.Printf("角色: %s, 状态: %v\n", user.Role, user.IsActive)

		// 验证密码
		err = bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(newPassword))
		if err != nil {
			fmt.Printf("❌ 密码验证失败: %v\n", err)
		} else {
			fmt.Printf("✅ 密码验证成功\n")
		}
	}

	fmt.Println("\n🎉 管理员密码修复完成！")
	fmt.Println("现在可以使用以下账户登录:")
	fmt.Println("超级管理员 - 手机号: 13800000001, 密码: 123456")
	fmt.Println("题库管理员1 - 手机号: 13800000002, 密码: 123456")
	fmt.Println("题库管理员2 - 手机号: 13800000003, 密码: 123456")
}

// getEnv 获取环境变量，如果不存在则返回默认值
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
